"""
Common services for data consistency and business logic
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from django.db import transaction, models
from django.db.models import Sum, F
from django.core.exceptions import ValidationError
from django.utils import timezone
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

class DataConsistencyService:
    """
    Service for maintaining data consistency across the application
    """
    
    @staticmethod
    @transaction.atomic
    def recalculate_invoice_total(invoice):
        """
        Recalculate and update invoice total based on invoice items
        """
        from billing.models import Invoice, InvoiceItem
        
        if not isinstance(invoice, Invoice):
            raise ValueError("Expected Invoice instance")
        
        # Calculate total from invoice items
        calculated_total = invoice.invoice_items.aggregate(
            total=Sum(F('quantity') * F('selling_price'))
        )['total'] or Decimal('0.00')
        
        # Update if different
        if abs(invoice.total_amount - calculated_total) > Decimal('0.01'):
            old_total = invoice.total_amount
            invoice.total_amount = calculated_total
            invoice.save(update_fields=['total_amount'])
            
            logger.info(f"Updated Invoice {invoice.id} total from {old_total} to {calculated_total}")
            return True
        
        return False
    
    @staticmethod
    @transaction.atomic
    def fix_all_invoice_totals():
        """
        Fix all invoice totals in the system
        """
        from billing.models import Invoice
        
        fixed_count = 0
        for invoice in Invoice.objects.all():
            if DataConsistencyService.recalculate_invoice_total(invoice):
                fixed_count += 1
        
        logger.info(f"Fixed {fixed_count} invoice totals")
        return fixed_count
    
    @staticmethod
    @transaction.atomic
    def create_missing_exchange_rates():
        """
        Create missing exchange rates for all currencies to base currency
        """
        from items.models import Currency, ExchangeRate
        
        base_currency = Currency.objects.filter(code='ALL').first()
        if not base_currency:
            logger.error("Base currency 'ALL' not found")
            return 0
        
        # Default exchange rates (these should be updated with real rates)
        default_rates = {
            'USD': Decimal('92.50'),  # 1 USD = 92.50 ALL
            'EUR': Decimal('101.20'), # 1 EUR = 101.20 ALL
            'GBP': Decimal('117.80'), # 1 GBP = 117.80 ALL
            'CHF': Decimal('103.45'), # 1 CHF = 103.45 ALL
            'CAD': Decimal('68.30'),  # 1 CAD = 68.30 ALL
            'AUD': Decimal('61.20'),  # 1 AUD = 61.20 ALL
            'JPY': Decimal('0.62'),   # 1 JPY = 0.62 ALL
            'CNY': Decimal('12.85'),  # 1 CNY = 12.85 ALL
            'SEK': Decimal('8.45'),   # 1 SEK = 8.45 ALL
            'NZD': Decimal('56.70'),  # 1 NZD = 56.70 ALL
        }
        
        created_count = 0
        for currency in Currency.objects.exclude(code='ALL'):
            # Check if exchange rate exists
            rate_exists = ExchangeRate.objects.filter(
                from_currency=currency,
                to_currency=base_currency
            ).exists()
            
            if not rate_exists:
                rate_value = default_rates.get(currency.code, Decimal('1.00'))
                ExchangeRate.objects.create(
                    from_currency=currency,
                    to_currency=base_currency,
                    rate=rate_value,
                    date=timezone.now().date()
                )
                created_count += 1
                logger.info(f"Created exchange rate: {currency.code} to ALL = {rate_value}")
        
        return created_count
    
    @staticmethod
    def validate_case_data(case_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate case data before creation/update
        """
        errors = {}
        
        # Check required fields
        required_fields = ['dentist', 'patient']
        for field in required_fields:
            if not case_data.get(field):
                errors.setdefault(field, []).append(f"{field} is required")
        
        # Validate dates
        if case_data.get('deadline') and case_data.get('received_date_time'):
            if case_data['deadline'] < case_data['received_date_time']:
                errors.setdefault('deadline', []).append("Deadline cannot be before received date")
        
        # Validate priority
        priority = case_data.get('priority')
        if priority is not None and not (1 <= priority <= 5):
            errors.setdefault('priority', []).append("Priority must be between 1 and 5")
        
        return errors
    
    @staticmethod
    def validate_financial_transaction(transaction_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate financial transaction data
        """
        errors = {}
        
        # Check amount is positive
        amount = transaction_data.get('amount')
        if amount is not None and amount <= 0:
            errors.setdefault('amount', []).append("Amount must be positive")
        
        # Check currency exists
        currency = transaction_data.get('currency')
        if currency:
            from items.models import Currency
            if not Currency.objects.filter(id=currency).exists():
                errors.setdefault('currency', []).append("Invalid currency")
        
        # Check account exists for payments
        account = transaction_data.get('account')
        if account:
            from finance.models import Account
            if not Account.objects.filter(id=account).exists():
                errors.setdefault('account', []).append("Invalid account")
        
        return errors
    
    @staticmethod
    @transaction.atomic
    def sync_case_schedule_status(case):
        """
        Synchronize case and schedule status
        """
        from case.models import Case
        from scheduling.models import Schedule
        
        if not hasattr(case, 'schedule'):
            return False
        
        schedule = case.schedule
        status_changed = False
        
        # Update schedule status based on case status
        if case.status == 'completed' and schedule.status != 'completed':
            schedule.status = 'completed'
            schedule.actual_end_date = timezone.now()
            schedule.save(update_fields=['status', 'actual_end_date'])
            status_changed = True
        
        elif case.status == 'cancelled' and schedule.status != 'cancelled':
            schedule.status = 'cancelled'
            schedule.save(update_fields=['status'])
            status_changed = True
        
        elif case.status == 'in_progress' and schedule.status == 'pending':
            schedule.status = 'in_progress'
            schedule.actual_start_date = timezone.now()
            schedule.save(update_fields=['status', 'actual_start_date'])
            status_changed = True
        
        if status_changed:
            logger.info(f"Synchronized status for Case {case.case_number} and Schedule {schedule.id}")
        
        return status_changed
    
    @staticmethod
    @transaction.atomic
    def fix_all_status_inconsistencies():
        """
        Fix all status inconsistencies in the system
        """
        from case.models import Case
        from billing.models import Invoice
        
        fixed_count = 0
        
        # Fix case-schedule status mismatches
        for case in Case.objects.filter(schedule__isnull=False):
            if DataConsistencyService.sync_case_schedule_status(case):
                fixed_count += 1
        
        # Fix invoice status for cancelled cases
        for invoice in Invoice.objects.filter(case__status='cancelled').exclude(status='cancelled'):
            invoice.status = 'cancelled'
            invoice.save(update_fields=['status'])
            fixed_count += 1
            logger.info(f"Cancelled Invoice {invoice.id} for cancelled case {invoice.case.case_number}")
        
        return fixed_count

class CalculationService:
    """
    Service for centralized calculations
    """
    
    @staticmethod
    def calculate_invoice_total(invoice_items) -> Decimal:
        """
        Calculate total amount from invoice items
        """
        total = Decimal('0.00')
        for item in invoice_items:
            item_total = item.quantity * item.selling_price
            total += item_total
        
        return total.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_case_cost_estimate(case) -> Decimal:
        """
        Calculate estimated cost for a case based on items
        """
        total_cost = Decimal('0.00')
        
        for case_item in case.case_items.all():
            item_cost = case_item.item.cost() * case_item.quantity
            total_cost += item_cost
        
        return total_cost.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_payment_allocation_remaining(payment) -> Decimal:
        """
        Calculate remaining amount available for allocation from a payment
        """
        from finance.models import InvoicePayment
        
        allocated_amount = InvoicePayment.objects.filter(
            payment=payment
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        remaining = payment.amount - allocated_amount
        return remaining.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

class ValidationService:
    """
    Service for centralized validation logic
    """
    
    @staticmethod
    def validate_workflow_transition(case, target_stage):
        """
        Validate if a case can transition to a target workflow stage
        """
        errors = []
        
        if not case.workflow_template:
            errors.append("Case has no workflow template assigned")
            return errors
        
        if target_stage.workflow != case.workflow_template:
            errors.append("Target stage does not belong to case workflow template")
            return errors
        
        # Check if all dependencies are completed
        if hasattr(target_stage, 'dependencies'):
            incomplete_deps = target_stage.dependencies.exclude(
                stagehistory__status='completed'
            )
            if incomplete_deps.exists():
                dep_names = [dep.name for dep in incomplete_deps]
                errors.append(f"Incomplete dependencies: {', '.join(dep_names)}")
        
        return errors
    
    @staticmethod
    def validate_payment_allocation(payment, invoice, amount):
        """
        Validate payment allocation to invoice
        """
        errors = []
        
        # Check if payment has enough remaining amount
        remaining = CalculationService.calculate_payment_allocation_remaining(payment)
        if amount > remaining:
            errors.append(f"Amount {amount} exceeds remaining payment amount {remaining}")
        
        # Check if invoice needs this much payment
        invoice_remaining = invoice.total_amount - (invoice.get_paid_amount() or Decimal('0.00'))
        if amount > invoice_remaining:
            errors.append(f"Amount {amount} exceeds invoice remaining amount {invoice_remaining}")
        
        # Check currency compatibility
        if payment.currency != invoice.currency:
            # Should have exchange rate available
            from items.models import ExchangeRate
            rate = ExchangeRate.get_exchange_rate(
                payment.currency.code, 
                invoice.currency.code
            )
            if not rate:
                errors.append(f"No exchange rate available from {payment.currency.code} to {invoice.currency.code}")
        
        return errors
