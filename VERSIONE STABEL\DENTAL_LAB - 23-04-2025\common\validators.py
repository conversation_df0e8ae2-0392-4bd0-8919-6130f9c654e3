"""
Custom validators for data integrity
"""

from decimal import Decimal
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

def validate_positive_amount(value):
    """Validate that an amount is positive"""
    if value <= 0:
        raise ValidationError(_('Amount must be positive'))

def validate_priority_range(value):
    """Validate that priority is within valid range (1-5)"""
    if not (1 <= value <= 5):
        raise ValidationError(_('Priority must be between 1 and 5'))

def validate_future_date(value):
    """Validate that a date is not in the past"""
    if value and value < timezone.now().date():
        raise ValidationError(_('Date cannot be in the past'))

def validate_deadline_after_received(received_date, deadline):
    """Validate that deadline is after received date"""
    if received_date and deadline and deadline < received_date:
        raise ValidationError(_('Deadline cannot be before received date'))

def validate_exchange_rate(value):
    """Validate exchange rate is positive and reasonable"""
    if value <= 0:
        raise ValidationError(_('Exchange rate must be positive'))
    if value > 10000:  # Reasonable upper limit
        raise ValidationError(_('Exchange rate seems unreasonably high'))

def validate_percentage(value):
    """Validate percentage is between 0 and 100"""
    if not (0 <= value <= 100):
        raise ValidationError(_('Percentage must be between 0 and 100'))

def validate_phone_number(value):
    """Validate phone number format"""
    import re
    if value and not re.match(r'^\+?[\d\s\-\(\)]{8,15}$', value):
        raise ValidationError(_('Invalid phone number format'))

def validate_email_domain(value):
    """Validate email domain is not from disposable email providers"""
    if value:
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com'
        ]
        domain = value.split('@')[-1].lower()
        if domain in disposable_domains:
            raise ValidationError(_('Disposable email addresses are not allowed'))

class BusinessRuleValidator:
    """Validator for complex business rules"""
    
    @staticmethod
    def validate_case_creation(case_data):
        """Validate case creation business rules"""
        errors = {}
        
        # Required fields
        if not case_data.get('dentist'):
            errors['dentist'] = _('Dentist is required')
        
        if not case_data.get('patient'):
            errors['patient'] = _('Patient is required')
        
        # Date validations
        received_date = case_data.get('received_date_time')
        deadline = case_data.get('deadline')
        
        if received_date and deadline:
            validate_deadline_after_received(received_date, deadline)
        
        # Priority validation
        priority = case_data.get('priority', 2)
        validate_priority_range(priority)
        
        return errors
    
    @staticmethod
    def validate_invoice_creation(invoice_data):
        """Validate invoice creation business rules"""
        errors = {}
        
        # Required fields
        if not invoice_data.get('case'):
            errors['case'] = _('Case is required')
        
        if not invoice_data.get('dentist'):
            errors['dentist'] = _('Dentist is required')
        
        # Amount validation
        total_amount = invoice_data.get('total_amount', Decimal('0.00'))
        validate_positive_amount(total_amount)
        
        # Date validation
        due_date = invoice_data.get('due_date')
        invoice_date = invoice_data.get('date')
        
        if due_date and invoice_date and due_date < invoice_date:
            errors['due_date'] = _('Due date cannot be before invoice date')
        
        return errors
    
    @staticmethod
    def validate_payment_allocation(payment, invoice, amount):
        """Validate payment allocation business rules"""
        errors = []
        
        # Amount validations
        validate_positive_amount(amount)
        
        # Check payment has sufficient remaining amount
        from common.services import CalculationService
        remaining = CalculationService.calculate_payment_allocation_remaining(payment)
        if amount > remaining:
            errors.append(_('Amount exceeds remaining payment amount'))
        
        # Check invoice needs this payment
        paid_amount = getattr(invoice, 'get_paid_amount', lambda: Decimal('0.00'))()
        invoice_remaining = invoice.total_amount - paid_amount
        if amount > invoice_remaining:
            errors.append(_('Amount exceeds invoice remaining amount'))
        
        # Currency compatibility
        if payment.currency != invoice.currency:
            from items.models import ExchangeRate
            rate = ExchangeRate.get_exchange_rate(
                payment.currency.code, 
                invoice.currency.code
            )
            if not rate:
                errors.append(_('No exchange rate available for currency conversion'))
        
        return errors
    
    @staticmethod
    def validate_workflow_transition(case, target_stage):
        """Validate workflow stage transition"""
        errors = []
        
        if not case.workflow_template:
            errors.append(_('Case has no workflow template'))
            return errors
        
        if target_stage.workflow != case.workflow_template:
            errors.append(_('Target stage does not belong to case workflow'))
            return errors
        
        # Check stage order
        if case.current_stage and target_stage.order <= case.current_stage.order:
            errors.append(_('Cannot move to previous or same stage'))
        
        # Check dependencies (if implemented)
        # This would need to be implemented based on your dependency model
        
        return errors

class DataIntegrityValidator:
    """Validator for data integrity checks"""
    
    @staticmethod
    def validate_financial_consistency():
        """Validate financial data consistency"""
        from billing.models import Invoice
        from finance.models import Payment, InvoicePayment
        from django.db.models import Sum, F
        
        errors = []
        
        # Check invoice totals
        for invoice in Invoice.objects.all():
            calculated_total = invoice.invoice_items.aggregate(
                total=Sum(F('quantity') * F('selling_price'))
            )['total'] or Decimal('0.00')
            
            if abs(invoice.total_amount - calculated_total) > Decimal('0.01'):
                errors.append(f'Invoice {invoice.id} total mismatch')
        
        # Check payment allocations
        for payment in Payment.objects.all():
            allocated = InvoicePayment.objects.filter(
                payment=payment
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            if allocated > payment.amount:
                errors.append(f'Payment {payment.id} over-allocated')
        
        return errors
    
    @staticmethod
    def validate_status_consistency():
        """Validate status consistency across related objects"""
        from case.models import Case
        from billing.models import Invoice
        
        errors = []
        
        # Check case-schedule status consistency
        for case in Case.objects.filter(schedule__isnull=False):
            schedule = case.schedule
            if case.status == 'completed' and schedule.status != 'completed':
                errors.append(f'Case {case.case_number} status inconsistent with schedule')
        
        # Check invoice-case status consistency
        for invoice in Invoice.objects.all():
            if invoice.case.status == 'cancelled' and invoice.status != 'cancelled':
                errors.append(f'Invoice {invoice.id} status inconsistent with cancelled case')
        
        return errors
    
    @staticmethod
    def validate_currency_consistency():
        """Validate currency and exchange rate consistency"""
        from items.models import Currency, ExchangeRate
        
        errors = []
        
        base_currency = Currency.objects.filter(code='ALL').first()
        if not base_currency:
            errors.append('Base currency ALL not found')
            return errors
        
        # Check for missing exchange rates
        for currency in Currency.objects.exclude(code='ALL'):
            rate_exists = ExchangeRate.objects.filter(
                from_currency=currency,
                to_currency=base_currency
            ).exists()
            
            if not rate_exists:
                errors.append(f'Missing exchange rate from {currency.code} to ALL')
        
        return errors

def run_full_validation():
    """Run all validation checks"""
    all_errors = []
    
    # Financial consistency
    financial_errors = DataIntegrityValidator.validate_financial_consistency()
    all_errors.extend([f"Financial: {error}" for error in financial_errors])
    
    # Status consistency
    status_errors = DataIntegrityValidator.validate_status_consistency()
    all_errors.extend([f"Status: {error}" for error in status_errors])
    
    # Currency consistency
    currency_errors = DataIntegrityValidator.validate_currency_consistency()
    all_errors.extend([f"Currency: {error}" for error in currency_errors])
    
    return all_errors
